/* Pink Theme Styles */
.pink-theme * {
    color: #c2185b !important; /* Deep Pink */
}

.pink-theme .app > * {
    background-color: #f8bbd0 !important; /* Pink 200 */
}

.pink-theme #nav {
    background-color: #e91e63; /* Pink 500 */
}

/* Add more specific styles as needed */
.pink-theme .bg-white.dark\:bg-gray-800 {
    background: #f48fb1; /* Pink 300 */
}

.pink-theme #chat-input {
    background: #f06292; /* Pink 400 */
    color: #fff !important;
}

.pink-theme .dark .cm-content {
    color: #fff0f5 !important; /* Lavender Blush */
}

/* Example: Button styles */
.pink-theme button {
    background-color: #d81b60 !important; /* Pink 700 */
    color: #fff !important;
}

.pink-theme button:hover {
    background-color: #c2185b !important; /* Pink 800 */
}